# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE_URL=https://api.openai.com/v1

# Optional: Set model for document analysis
OPENAI_MODEL=gpt-4o-mini

# AI Prompt Configuration
# System prompt for document analysis
OPENAI_SYSTEM_PROMPT="你是一个专业的文档分析助手，擅长提取文档结构和生成目录。请直接返回JSON数组，不要包含其他解释文字。"

# User prompt template (use {fileType} and {content} as placeholders)
OPENAI_USER_PROMPT="分析{fileType}文档，将其分解为3-6个段落并生成总结。返回JSON对象，包含segments数组。\n\n要求：\n1. 按逻辑主题分解文档\n2. 生成简洁标题(10-25字)\n3. 写1句话总结概括核心内容\n4. 准确找到每个段落开始位置的前8-12个连续字符作为锚点(必须是原文中确实存在的字符)\n5. 锚点不能包含换行符、标点符号，应该是纯文字内容\n\n文档内容：\n{content}\n\n返回格式(确保JSON有效)：\n{\n  \"segments\": [\n    {\n      \"title\": \"段落标题\",\n      \"level\": 1,\n      \"summary\": \"一句话总结\",\n      \"anchor\": \"段落开始的连续文字\"\n    }\n  ]\n}"

# Content truncation settings
OPENAI_MAX_CONTENT_LENGTH=8000
OPENAI_TRUNCATION_MESSAGE="...(内容过长已截断)"