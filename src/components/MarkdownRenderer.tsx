'use client'

import { useRef } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import rehypeRaw from 'rehype-raw'

interface MarkdownRendererProps {
  content: string
  onDoubleClick?: (position: { x: number; y: number }) => void
}

export default function MarkdownRenderer({ content, onDoubleClick }: MarkdownRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  // 处理双击事件
  const handleDoubleClick = (e: React.MouseEvent) => {
    if (!onDoubleClick) return
    
    // 计算双击位置
    const clickX = e.clientX
    const clickY = e.clientY
    
    onDoubleClick({ x: clickX, y: clickY })
  }

  return (
    <>
      <div 
        ref={containerRef}
        className="markdown-content prose prose-lg max-w-none cursor-text"
        onDoubleClick={handleDoubleClick}
      >
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw, rehypeHighlight]}
        >
          {content}
        </ReactMarkdown>
      </div>
    </>
  )
}