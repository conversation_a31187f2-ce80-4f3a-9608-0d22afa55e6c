'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'

interface FloatingChatProps {
  isVisible: boolean
  onClose: () => void
  position: { x: number; y: number }
  onPositionChange: (position: { x: number; y: number }) => void
  size: { width: number; height: number }
  onSizeChange: (size: { width: number; height: number }) => void
  originalContent: string
  forceExpanded?: boolean
  onExpandedChange?: (expanded: boolean) => void
}

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

export default function FloatingChat({
  isVisible,
  onClose,
  position,
  onPositionChange,
  size,
  onSizeChange,
  originalContent,
  forceExpanded = false,
  onExpandedChange
}: FloatingChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [resizeDirection, setResizeDirection] = useState<string>('')
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [isExpanded, setIsExpanded] = useState(false)
  
  // 监听 forceExpanded 变化
  useEffect(() => {
    if (forceExpanded) {
      setIsExpanded(true)
      onExpandedChange?.(false) // 清除强制展开标志
    }
  }, [forceExpanded, onExpandedChange])
  
  // 调试：监听isExpanded变化
  useEffect(() => {
    console.log('isExpanded 状态变化:', isExpanded)
  }, [isExpanded])

  const chatRef = useRef<HTMLDivElement>(null)
  const messagesRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const lastMoveTime = useRef<number>(0)
  const animationId = useRef<number | null>(null)
  const windowBounds = useRef({ width: 0, height: 0 })

  // 超平滑拖拽功能 - 消除抖动
  const handleMouseDown = (e: React.MouseEvent) => {
    if (isResizing) return
    
    // 简化的元素检查
    const target = e.target as HTMLElement
    const tagName = target.tagName.toLowerCase()
    if (tagName === 'textarea' || tagName === 'button' || tagName === 'input' || tagName === 'i') {
      return
    }
    
    // 检查是否点击了按钮内的元素
    if (target.closest('button')) {
      return
    }
    
    // 使用精确计算避免抖动
    const rect = chatRef.current?.getBoundingClientRect()
    if (!rect) return
    
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })
    setIsDragging(true)
    
    // 立即禁用选择和设置样式
    e.preventDefault()
    e.stopPropagation()
    if (chatRef.current) {
      chatRef.current.style.userSelect = 'none'
      chatRef.current.style.pointerEvents = 'none' // 防止子元素干扰
    }
    document.body.style.userSelect = 'none'
  }

  const handleMouseMove = useCallback((e: MouseEvent) => {
    const now = performance.now()
    
    // 节流：限制到 16ms (60fps)
    if (now - lastMoveTime.current < 16) {
      return
    }
    lastMoveTime.current = now
    
    if (isDragging) {
      // 取消之前的动画帧
      if (animationId.current) {
        cancelAnimationFrame(animationId.current)
      }
      
      // 批量计算避免重复
      const bounds = windowBounds.current
      const newX = Math.max(0, Math.min(e.clientX - dragOffset.x, bounds.width - size.width))
      const newY = Math.max(0, Math.min(e.clientY - dragOffset.y, bounds.height - size.height))
      
      // 只在真正需要时使用 requestAnimationFrame
      animationId.current = requestAnimationFrame(() => {
        onPositionChange({ x: newX, y: newY })
        animationId.current = null
      })
      return
    }
    
    if (isResizing) {
      handleResize(e)
    }
  }, [isDragging, isResizing, dragOffset, size, onPositionChange])

  const handleMouseUp = useCallback(() => {
    if (isDragging || isResizing) {
      // 恢复文本选择和样式
      if (chatRef.current) {
        chatRef.current.style.userSelect = ''
        chatRef.current.style.pointerEvents = ''
      }
      document.body.style.userSelect = ''
    }
    
    setIsDragging(false)
    setIsResizing(false)
    setResizeDirection('')
  }, [isDragging, isResizing])

  // 简化的缩放功能
  const handleResizeStart = (e: React.MouseEvent, direction: string) => {
    e.stopPropagation()
    e.preventDefault()
    setIsResizing(true)
    setResizeDirection(direction)
    
    if (chatRef.current) {
      chatRef.current.style.userSelect = 'none'
    }
  }

  const handleResize = (e: MouseEvent) => {
    if (!isResizing || !chatRef.current) return

    e.preventDefault()
    
    let newWidth = size.width
    let newHeight = size.height
    let newX = position.x
    let newY = position.y

    // 简化delta计算
    const mouseX = e.clientX
    const mouseY = e.clientY

    // 根据缩放方向调整尺寸和位置
    switch (resizeDirection) {
      case 'n': // 上边
        newHeight = position.y + size.height - mouseY
        newY = mouseY
        break
      case 's': // 下边
        newHeight = mouseY - position.y
        break
      case 'w': // 左边
        newWidth = position.x + size.width - mouseX
        newX = mouseX
        break
      case 'e': // 右边
        newWidth = mouseX - position.x
        break
      case 'nw': // 左上角
        newWidth = position.x + size.width - mouseX
        newHeight = position.y + size.height - mouseY
        newX = mouseX
        newY = mouseY
        break
      case 'ne': // 右上角
        newWidth = mouseX - position.x
        newHeight = position.y + size.height - mouseY
        newY = mouseY
        break
      case 'sw': // 左下角
        newWidth = position.x + size.width - mouseX
        newHeight = mouseY - position.y
        newX = mouseX
        break
      case 'se': // 右下角
        newWidth = mouseX - position.x
        newHeight = mouseY - position.y
        break
    }

    // 最小尺寸限制
    if (newWidth < 320) {
      newWidth = 320
      if (resizeDirection.includes('w')) newX = position.x + size.width - 320
    }
    if (newHeight < 240) {
      newHeight = 240
      if (resizeDirection.includes('n')) newY = position.y + size.height - 240
    }

    // 最大尺寸和位置限制
    if (newX < 0) {
      newWidth += newX
      newX = 0
    }
    if (newY < 0) {
      newHeight += newY
      newY = 0
    }
    if (newX + newWidth > window.innerWidth) {
      newWidth = window.innerWidth - newX
    }
    if (newY + newHeight > window.innerHeight) {
      newHeight = window.innerHeight - newY
    }

    onSizeChange({ width: newWidth, height: newHeight })
    if (newX !== position.x || newY !== position.y) {
      onPositionChange({ x: newX, y: newY })
    }
  }

  // 更新窗口边界缓存
  useEffect(() => {
    const updateBounds = () => {
      windowBounds.current = {
        width: window.innerWidth,
        height: window.innerHeight
      }
    }
    
    updateBounds()
    window.addEventListener('resize', updateBounds, { passive: true })
    return () => window.removeEventListener('resize', updateBounds)
  }, [])

  // 监听全局鼠标事件
  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove, { passive: true })
      document.addEventListener('mouseup', handleMouseUp)
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        // 清理动画帧
        if (animationId.current) {
          cancelAnimationFrame(animationId.current)
          animationId.current = null
        }
      }
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp])

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      const response = await fetch('/api/chat-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          context: originalContent,
          history: messages.slice(-4) // 保持最近4条消息作为历史
        })
      })

      if (!response.ok) {
        throw new Error('Failed to get response')
      }

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, assistantMessage])

      // 流式响应处理
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6))
                if (data.content) {
                  setMessages(prev => 
                    prev.map(msg => 
                      msg.id === assistantMessage.id 
                        ? { ...msg, content: msg.content + data.content }
                        : msg
                    )
                  )
                }
              } catch (e) {
                console.warn('Failed to parse SSE data:', e)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Chat error:', error)
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '抱歉，发生了错误，请稍后重试。',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  // 键盘事件处理
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 自动滚动到底部
  useEffect(() => {
    if (messagesRef.current) {
      messagesRef.current.scrollTop = messagesRef.current.scrollHeight
    }
  }, [messages])

  // 聚焦输入框
  useEffect(() => {
    if (isVisible && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100)
    }
  }, [isVisible])

  if (!isVisible || !originalContent || !isExpanded) return null

  return (
    <div
      ref={chatRef}
      className="fixed z-50 flex flex-col chat-floating-container chat-fade-in"
      style={{
        left: 0,
        top: 0,
        width: size.width,
        height: size.height,
        transform: `translate3d(${position.x}px, ${position.y}px, 0)`,
        cursor: isDragging ? 'grabbing' : 'default',
        willChange: isDragging ? 'transform' : 'auto'
      }}
    >
      {/* 主容器 - 毛玻璃效果 */}
      <div 
        className="chat-card h-full flex flex-col overflow-hidden cursor-grab active:cursor-grabbing"
        onMouseDown={handleMouseDown}
      >
        {/* 标题栏 */}
        <div className="flex items-center justify-between px-4 py-2 chat-header">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <div className="w-2 h-2 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full shadow-sm"></div>
              <div className="absolute inset-0 w-2 h-2 bg-blue-400 rounded-full animate-ping opacity-20"></div>
            </div>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">AI 助手</span>
          </div>
          <button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              onClose()
            }}
            className="p-1 hover:bg-white/10 dark:hover:bg-black/10 rounded-full transition-all duration-200 group"
            title="关闭"
          >
            <i className="fas fa-times text-gray-500 dark:text-gray-400 text-xs group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-colors"></i>
          </button>
        </div>

        {/* 消息区域 */}
        <div
          ref={messagesRef}
          className="flex-1 overflow-y-auto px-4 pb-4 space-y-4 chat-messages custom-scrollbar"
        >
          {messages.length === 0 && (
            <div className="text-center py-12 chat-empty-state">
            </div>
          )}
          {messages.map((message) => (
            <div
              key={message.id}
              className={`animate-fadeInUp ${message.role === 'user' ? 'flex justify-end' : ''}`}
            >
              {message.role === 'user' ? (
                <div className="max-w-[85%] chat-bubble chat-bubble-user">
                  <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
                </div>
              ) : (
                <div className="w-full chat-assistant-content">
                  <div className="prose prose-sm max-w-none dark:prose-invert chat-markdown">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      rehypePlugins={[rehypeHighlight]}
                    >
                      {message.content}
                    </ReactMarkdown>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* 输入区域 - 一体化设计 */}
        <div className="chat-input-container px-4 pb-2">
          <div className="chat-input-wrapper">
            <textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入您的问题... (Enter 发送，Shift+Enter 换行)"
              className="chat-input-unified"
              rows={1}
              disabled={isLoading}
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              className="chat-send-btn-unified"
              title="发送消息"
            >
              <i className={`fas ${isLoading ? 'fa-spinner fa-spin' : 'fa-paper-plane'} text-sm`}></i>
            </button>
          </div>
        </div>
      </div>

      {/* 缩放区域 - 四条边 */}
      <div className="absolute top-0 left-2 right-2 h-2 cursor-n-resize" onMouseDown={(e) => handleResizeStart(e, 'n')} />
      <div className="absolute bottom-0 left-2 right-2 h-2 cursor-s-resize" onMouseDown={(e) => handleResizeStart(e, 's')} />
      <div className="absolute left-0 top-2 bottom-2 w-2 cursor-w-resize" onMouseDown={(e) => handleResizeStart(e, 'w')} />
      <div className="absolute right-0 top-2 bottom-2 w-2 cursor-e-resize" onMouseDown={(e) => handleResizeStart(e, 'e')} />
      
      {/* 缩放区域 - 四个角 */}
      <div className="absolute top-0 left-0 w-4 h-4 cursor-nw-resize" onMouseDown={(e) => handleResizeStart(e, 'nw')} />
      <div className="absolute top-0 right-0 w-4 h-4 cursor-ne-resize" onMouseDown={(e) => handleResizeStart(e, 'ne')} />
      <div className="absolute bottom-0 left-0 w-4 h-4 cursor-sw-resize" onMouseDown={(e) => handleResizeStart(e, 'sw')} />
      <div className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize" onMouseDown={(e) => handleResizeStart(e, 'se')} />
    </div>
  )
}