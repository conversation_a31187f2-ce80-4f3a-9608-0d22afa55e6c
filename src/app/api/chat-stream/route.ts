import OpenAI from 'openai'
import { NextRequest, NextResponse } from 'next/server'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL,
})

export async function POST(request: NextRequest) {
  try {
    const { message, context, history } = await request.json()

    if (!message || typeof message !== 'string') {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 })
    }

    // 构建消息历史
    const messages: Array<{ role: 'system' | 'user' | 'assistant', content: string }> = [
      {
        role: 'system',
        content: `你是一个专业的文档助手。用户会基于以下文档内容向你提问，请根据文档内容准确回答问题。

文档内容：
${context || ''}

请注意：
1. **必须使用丰富的 Markdown 格式**，包含：
   - 使用 **粗体** 和 *斜体* 强调重点
   - 使用 > 引用块引用原文关键内容
   - 使用列表（- 或 1.）组织信息
   - 使用表格展示对比数据
   - 使用代码块 \`\`\` 展示代码示例
   - 使用 \`行内代码\` 标记术语
   - 使用 ### 标题分段组织回答
2. 基于文档内容回答，如果文档中没有相关信息，请明确说明
3. 回答要**准确、详细、结构化**
4. 适当使用卡片式布局，用分隔符 --- 分段
5. 引用原文时使用引用块格式`
      }
    ]

    // 添加历史消息
    if (history && Array.isArray(history)) {
      history.forEach((msg: any) => {
        if (msg.role && msg.content && ['user', 'assistant'].includes(msg.role)) {
          messages.push({
            role: msg.role,
            content: msg.content
          })
        }
      })
    }

    // 添加当前用户消息
    messages.push({
      role: 'user',
      content: message
    })

    // 创建流式响应
    const stream = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages,
      stream: true,
      temperature: 0.7,
      max_tokens: 2000,
    })

    // 创建可读流
    const encoder = new TextEncoder()
    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content
            if (content) {
              const data = `data: ${JSON.stringify({ content })}\n\n`
              controller.enqueue(encoder.encode(data))
            }
          }
          
          // 发送结束标志
          const endData = `data: ${JSON.stringify({ done: true })}\n\n`
          controller.enqueue(encoder.encode(endData))
          controller.close()
        } catch (error) {
          console.error('Stream error:', error)
          const errorData = `data: ${JSON.stringify({ error: 'Stream processing failed' })}\n\n`
          controller.enqueue(encoder.encode(errorData))
          controller.close()
        }
      }
    })

    return new Response(readableStream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {
    console.error('Chat API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}