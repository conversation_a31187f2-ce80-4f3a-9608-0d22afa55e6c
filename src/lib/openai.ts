import OpenAI from 'openai'
import { DocumentSection, MediaType, LocationInfo } from '@/types/media'

// 初始化 OpenAI 客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_API_BASE_URL || 'https://api.openai.com/v1',
})

// 保持向后兼容的简化接口
export interface LegacyDocumentSection {
  id: string
  title: string
  level: number
  summary: string
  anchor?: string
}

// 流式分析文档函数
export async function* analyzeDocumentStream(
  content: string, 
  fileType: string
): AsyncGenerator<{ type: 'progress' | 'section' | 'complete' | 'summary', data?: any }, void, unknown> {
  try {
    // 从环境变量获取配置
    const maxContentLength = parseInt(process.env.OPENAI_MAX_CONTENT_LENGTH || '8000')
    const truncationMessage = process.env.OPENAI_TRUNCATION_MESSAGE || '...(内容过长已截断)'
    const systemPrompt = process.env.OPENAI_SYSTEM_PROMPT || '你是一个专业的文档分析助手，擅长提取文档结构和生成目录。\n重要：请首先输出一个导读JSON对象，然后输出一系列章节JSON对象。每个对象独立输出，可以用空格、换行或直接连续输出。\n不要输出JSON数组，不要使用[]括号，不要输出segments等包装字段。'
    const userPromptTemplate = process.env.OPENAI_USER_PROMPT || `请分析以下{fileType}文档内容，生成一个结构化的目录大纲和文档导读。

要求：
1. 首先生成一个文档导读（不超过100字）
2. 然后识别文档的主要章节和子章节
3. 为每个章节生成一个简洁的标题
4. 使用合适的层级关系（1-3级）
5. 如果是技术文档，请保留技术术语
6. 如果内容较短，至少生成3-5个主要观点作为目录项
7. 为每个章节找到在原文中的锚点文本（前10-20个字符）

文档内容：
{content}

输出格式要求：
- 第一个输出必须是文档导读，格式：{"type":"summary","content":"导读内容"}
- 然后输出一系列独立的JSON对象，每个对象表示一个章节
- 可以连续输出多个JSON对象，用空格或换行分隔
- 不要使用JSON数组[]，不要包装在其他字段中

支持的输出格式示例：

{"type":"summary","content":"这是一篇关于技术实现的文档，详细介绍了核心概念和实践方法，适合开发者和技术爱好者阅读学习。"}
{"title":"引言","level":1,"summary":"介绍文档背景","anchor":"本文档旨在介绍"}
{"title":"核心概念","level":1,"summary":"说明主要概念","anchor":"核心概念包括以下"}`

    yield { type: 'progress', data: { message: '正在调用OpenAI API...', progress: 10 } }

    // 处理内容截断
    const truncatedContent = content.substring(0, maxContentLength) + 
      (content.length > maxContentLength ? truncationMessage : '')

    // 替换模板中的占位符
    const userPrompt = userPromptTemplate
      .replace('{fileType}', fileType)
      .replace('{content}', truncatedContent)

    yield { type: 'progress', data: { message: '发送分析请求...', progress: 20 } }

    // 使用流式API
    const stream = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: userPrompt
        }
      ],
      temperature: 0.3,
      max_tokens: 1000,
      stream: true // 启用流式输出
    })

    yield { type: 'progress', data: { message: '正在接收AI分析结果...', progress: 30 } }

    let buffer = ''
    let sectionIndex = 0
    const sections: DocumentSection[] = []
    let documentSummary: string | null = null // 存储文档导读
    let isJsonArray = false // 检测是否为传统JSON数组格式
    let fullResponse = '' // 保存完整响应用于后备处理
    let braceCount = 0 // 跟踪花括号深度
    let inString = false // 是否在字符串中
    let escapeNext = false // 下一个字符是否被转义
    
    // 辅助函数：从buffer中提取完整的JSON对象
    const extractJsonObjects = (text: string): { objects: any[], remaining: string } => {
      const objects: any[] = []
      let startIdx = 0
      let depth = 0
      let inStr = false
      let escape = false
      
      for (let i = 0; i < text.length; i++) {
        const char = text[i]
        
        if (escape) {
          escape = false
          continue
        }
        
        if (char === '\\') {
          escape = true
          continue
        }
        
        if (char === '"' && !escape) {
          inStr = !inStr
          continue
        }
        
        if (!inStr) {
          if (char === '{') {
            if (depth === 0) startIdx = i
            depth++
          } else if (char === '}') {
            depth--
            if (depth === 0) {
              // 找到完整的JSON对象
              const jsonStr = text.substring(startIdx, i + 1)
              try {
                const obj = JSON.parse(jsonStr)
                if (obj.type === 'summary' || obj.title) { // 导读或章节对象
                  objects.push(obj)
                  console.log('提取到JSON对象:', obj)
                }
              } catch (e) {
                console.log('JSON解析失败，继续寻找:', jsonStr.substring(0, 50) + '...')
              }
            }
          }
        }
      }
      
      // 返回剩余的未完成部分
      const remaining = depth > 0 ? text.substring(startIdx) : ''
      return { objects, remaining }
    }
    
    // 流式处理响应 - 支持多种JSON流格式
    for await (const chunk of stream) {
      const delta = chunk.choices[0]?.delta?.content || ''
      if (delta) {
        buffer += delta
        fullResponse += delta
        console.log('OpenAI流式chunk:', delta)
        
        // 检测是否为传统JSON格式
        if (!isJsonArray && (buffer.includes('"segments"') || buffer.trimStart().startsWith('['))) {
          isJsonArray = true
          console.log('检测到传统JSON格式，切换到后备处理模式')
          continue // 继续收集完整响应
        }
        
        // 如果是JSON数组格式，跳过流式解析
        if (isJsonArray) {
          continue
        }
        
        // 尝试提取完整的JSON对象
        const { objects, remaining } = extractJsonObjects(buffer)
        buffer = remaining
        
        // 处理提取到的对象
        for (const item of objects) {
          console.log('成功解析对象:', item)
          console.log('对象类型:', item.type, '是否为导读:', item.type === 'summary')
          
          // 处理文档导读
          if (item.type === 'summary' && !documentSummary) {
            documentSummary = item.content
            console.log('提取到文档导读:', documentSummary)
            
            // 流式返回导读
            yield { 
              type: 'summary', 
              data: { 
                summary: documentSummary
              } 
            }
            continue
          }
          
          // 处理章节
          if (item.title) {
            // 立即转换为标准格式并流式返回
            const section: DocumentSection = {
              id: `section-${sectionIndex + 1}`,
              title: item.title || `段落 ${sectionIndex + 1}`,
              level: item.level || 1,
              summary: item.summary || '该段落的内容总结',
              mediaType: MediaType.TEXT,
              location: {
                textAnchor: item.anchor,
                charPosition: item.startChar
              } as LocationInfo
            }
            
            sections.push(section)
            
            // 实时流式返回章节
            yield { 
              type: 'section', 
              data: { 
                section, 
                index: sectionIndex, 
                total: -1 // 总数未知，因为是流式的
              } 
            }
            
            sectionIndex++
            
            // 更新进度
            yield { 
              type: 'progress', 
              data: { 
                message: `已识别 ${sectionIndex} 个章节...`, 
                progress: Math.min(30 + (sectionIndex * 10), 90)
              } 
            }
          }
        }
      }
    }
    
    // 如果检测到传统JSON格式，进行后备处理
    if (isJsonArray && sections.length === 0) {
      console.log('使用后备处理：解析传统JSON格式')
      try {
        // 清理响应
        let cleanedResult = fullResponse.trim()
        cleanedResult = cleanedResult.replace(/^```json\s*/i, '').replace(/\s*```$/i, '')
        
        let parsedResult
        // 尝试解析完整JSON
        try {
          parsedResult = JSON.parse(cleanedResult)
          if (!Array.isArray(parsedResult)) {
            parsedResult = parsedResult.segments || parsedResult.sections || parsedResult.toc || parsedResult.outline || []
          }
        } catch (e) {
          // 尝试提取JSON数组
          const jsonMatch = cleanedResult.match(/\[\s*\{[\s\S]*\}\s*\]/)
          if (jsonMatch) {
            parsedResult = JSON.parse(jsonMatch[0])
          } else {
            throw new Error('无法解析AI返回的格式')
          }
        }
        
        // 转换并流式返回章节
        for (let i = 0; i < parsedResult.length; i++) {
          const item = parsedResult[i]
          const section: DocumentSection = {
            id: `section-${i + 1}`,
            title: item.title || `段落 ${i + 1}`,
            level: item.level || 1,
            summary: item.summary || '该段落的内容总结',
            mediaType: MediaType.TEXT,
            location: {
              textAnchor: item.anchor,
              charPosition: item.startChar
            } as LocationInfo
          }
          
          sections.push(section)
          
          yield { 
            type: 'section', 
            data: { 
              section, 
              index: i, 
              total: parsedResult.length 
            } 
          }
        }
      } catch (e) {
        console.error('后备处理失败:', e)
        throw new Error('AI返回的格式无效')
      }
    } else if (!isJsonArray && buffer.trim()) {
      // 处理最后可能残留的NDJSON内容
      try {
        const item = JSON.parse(buffer.trim())
        const section: DocumentSection = {
          id: `section-${sectionIndex + 1}`,
          title: item.title || `段落 ${sectionIndex + 1}`,
          level: item.level || 1,
          summary: item.summary || '该段落的内容总结',
          mediaType: MediaType.TEXT,
          location: {
            textAnchor: item.anchor,
            charPosition: item.startChar
          } as LocationInfo
        }
        
        sections.push(section)
        
        yield { 
          type: 'section', 
          data: { 
            section, 
            index: sectionIndex, 
            total: sections.length
          } 
        }
      } catch (e) {
        console.log('最后的buffer不是有效JSON:', buffer)
      }
    }

    yield { 
      type: 'complete', 
      data: { 
        message: '分析完成', 
        sectionsCount: sections.length,
        sections,
        documentSummary
      } 
    }

  } catch (error) {
    console.error('流式文档分析失败:', error)
    throw error
  }
}

// 保持向后兼容的非流式函数
export async function analyzeDocument(content: string, fileType: string): Promise<DocumentSection[]> {
  const sections: DocumentSection[] = []
  
  for await (const event of analyzeDocumentStream(content, fileType)) {
    if (event.type === 'section') {
      sections.push(event.data.section)
    } else if (event.type === 'complete') {
      return event.data.sections
    }
  }
  
  return sections
}

export function convertSectionsToMarkdown(sections: DocumentSection[], originalContent: string): string {
  // 只处理原始内容，为每个段落添加锚点标记
  let processedContent = originalContent
  
  // 为每个段落查找锚点并插入标记
  sections.forEach((section) => {
    const anchor = section.location.textAnchor
    if (anchor) {
      const anchorIndex = processedContent.indexOf(anchor)
      if (anchorIndex !== -1) {
        // 在锚点前插入span标记
        const beforeAnchor = processedContent.substring(0, anchorIndex)
        const afterAnchor = processedContent.substring(anchorIndex)
        processedContent = beforeAnchor + `<span id="${section.id}"></span>` + afterAnchor
      }
    }
  })
  
  return processedContent
}