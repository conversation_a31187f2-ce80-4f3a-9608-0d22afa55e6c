# 多媒体支持架构设计

## 🎯 概述

本项目已完成多媒体支持的通用接口框架设计，为后续支持 PDF、视频、音频、网页等多种媒体类型的跳转功能预留了完整的扩展性。

## 📁 文件结构

```
src/
├── types/
│   └── media.ts                    # 媒体类型定义和接口
├── services/
│   └── media/
│       ├── index.ts                # 媒体服务统一出口
│       ├── base.ts                 # 媒体处理器基类和工厂模式
│       ├── text.ts                 # 文本媒体处理器
│       └── processors.ts           # 其他媒体处理器（预留）
├── components/
│   └── MediaViewer/
│       ├── index.tsx               # 通用媒体视图器
│       ├── TextViewer.tsx          # 文本视图器
│       └── ViewerStubs.tsx         # 其他视图器（预留）
└── app/api/analyze/
    └── route.ts                    # 支持多媒体类型的API路由
```

## 🔧 核心架构

### 1. 媒体类型枚举

```typescript
enum MediaType {
  TEXT = 'text',
  MARKDOWN = 'markdown', 
  PDF = 'pdf',
  VIDEO = 'video',
  AUDIO = 'audio',
  WEBPAGE = 'webpage',
  IMAGE = 'image'
}
```

### 2. 统一定位接口

```typescript
interface LocationInfo {
  // 文本定位
  textAnchor?: string         // 文本锚点
  charPosition?: number       // 字符位置
  lineNumber?: number         // 行号
  
  // PDF定位
  pageNumber?: number         // 页号
  pdfCoordinates?: { x, y, width, height }
  
  // 视频/音频定位
  timestamp?: number          // 时间戳（秒）
  timeRange?: { start, end }  // 时间范围
  
  // 网页定位
  urlHash?: string            // URL片段
  cssSelector?: string        // CSS选择器
  xPath?: string              // XPath
  
  // 图片定位
  imageCoordinates?: { x, y, width, height }
}
```

### 3. 媒体处理器工厂模式

```typescript
abstract class MediaProcessor {
  abstract parseFile(file: File): Promise<MediaFile>
  abstract extractContent(mediaFile: MediaFile): Promise<string>
  abstract processAnalysisResult(sections: any[], mediaFile: MediaFile): Promise<DocumentSection[]>
  abstract navigate(location: LocationInfo, mediaFile: MediaFile): Promise<void>
}

class MediaProcessorFactory {
  static register(mediaType: MediaType, processor: MediaProcessor)
  static getProcessor(mediaType: MediaType): MediaProcessor | null
}
```

## 🚀 功能特性

### 1. **通用性设计**
- 所有媒体类型使用统一的 `DocumentSection` 接口
- 灵活的 `LocationInfo` 支持多种定位方式
- 可扩展的媒体处理器架构

### 2. **向后兼容**
- 保持现有文本/Markdown功能完全正常
- 新增接口不影响现有API
- 渐进式迁移支持

### 3. **扩展性预留**
- 工厂模式支持动态注册新的媒体处理器
- 统一的媒体视图器组件框架
- 可配置的AI分析参数

## 📋 各媒体类型跳转方案

| 媒体类型 | 定位方式 | 跳转实现 | 开发状态 |
|---------|----------|----------|----------|
| **文本** | textAnchor, charPosition | scrollIntoView | ✅ 已完成 |
| **Markdown** | textAnchor, charPosition | scrollIntoView | ✅ 已完成 |
| **PDF** | pageNumber, coordinates | pdf.js API | 🔄 接口已预留 |
| **视频** | timestamp, timeRange | video.currentTime | 🔄 接口已预留 |
| **音频** | timestamp, timeRange | audio.currentTime | 🔄 接口已预留 |
| **网页** | cssSelector, urlHash | querySelector | 🔄 接口已预留 |
| **图片** | imageCoordinates | 坐标定位 | 🔄 接口已预留 |

## 🛠 实现示例

### PDF跳转（预留实现）
```typescript
class PDFMediaProcessor extends MediaProcessor {
  async navigate(location: LocationInfo, mediaFile: MediaFile): Promise<void> {
    if (location.pageNumber) {
      // 使用 pdf.js 跳转到指定页面
      pdfViewer.goToPage(location.pageNumber)
      
      if (location.pdfCoordinates) {
        // 滚动到页面内的具体坐标
        pdfViewer.scrollTo(location.pdfCoordinates)
      }
    }
  }
}
```

### 视频跳转（预留实现）
```typescript
class VideoMediaProcessor extends MediaProcessor {
  async navigate(location: LocationInfo, mediaFile: MediaFile): Promise<void> {
    if (location.timestamp !== undefined) {
      const video = document.querySelector('video')
      if (video) {
        video.currentTime = location.timestamp
        video.play()
      }
    }
  }
}
```

## 🔮 后续开发计划

### 阶段1：PDF支持
- [ ] 集成 pdf.js 
- [ ] 实现页面级跳转
- [ ] 实现坐标级定位
- [ ] AI提示词适配PDF分析

### 阶段2：视频支持  
- [ ] 集成视频播放器
- [ ] 实现时间戳跳转
- [ ] 字幕/章节提取
- [ ] AI提示词适配视频分析

### 阶段3：音频支持
- [ ] 集成音频播放器
- [ ] 实现时间戳跳转  
- [ ] 音频转文字
- [ ] 波形可视化

### 阶段4：网页支持
- [ ] HTML解析和渲染
- [ ] CSS选择器跳转
- [ ] 元素高亮
- [ ] 响应式适配

## ✅ 当前状态

- ✅ **接口框架设计完成**：所有媒体类型的接口已定义
- ✅ **工厂模式实现**：支持动态注册媒体处理器
- ✅ **文本支持完整**：文本/Markdown 完全支持跳转
- ✅ **组件框架就绪**：MediaViewer 支持切换不同媒体类型
- ✅ **API路由适配**：后端已支持多媒体类型检测

这个架构设计确保了：
1. **当前功能不受影响**
2. **未来扩展非常简单**
3. **代码复用性高**
4. **接口设计合理**

各种媒体类型的具体实现可以根据需求优先级逐步开发。