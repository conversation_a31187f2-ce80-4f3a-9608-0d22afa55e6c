# AI 文档分析上下文说明

## 提交给 AI 的内容包括

### 1. System Prompt（系统提示词）
```
你是一个专业的文档分析助手，擅长提取文档结构和生成目录。请直接返回JSON数组，不要包含其他解释文字。
```
- **作用**：设定 AI 的角色和输出格式要求
- **可配置**：通过 `OPENAI_SYSTEM_PROMPT` 环境变量

### 2. User Prompt（用户提示词）
包含以下信息：
- **文件类型**：如 `txt`、`pdf`、`doc` 等
- **分析要求**：
  1. 识别文档的主要章节和子章节
  2. 为每个章节生成一个简洁的标题
  3. 使用合适的层级关系（1-3级）
  4. 如果是技术文档，请保留技术术语
  5. 如果内容较短，至少生成3-5个主要观点作为目录项
- **文档内容**：
  - 最多发送前 8000 个字符（可通过 `OPENAI_MAX_CONTENT_LENGTH` 配置）
  - 超过限制会截断并添加提示信息
- **返回格式要求**：JSON 数组格式的目录结构

### 3. API 参数设置
- **模型**：默认 `gpt-4o-mini`（可通过 `OPENAI_MODEL` 配置）
- **温度**：0.3（较低温度确保输出稳定性）
- **最大令牌数**：1000（足够生成详细的目录）
- **响应格式**：`json_object`（强制 JSON 格式输出）

## 数据流程

1. **用户上传文档** → 前端识别文件类型
2. **非 Markdown 文档** → 发送到后端 API
3. **后端处理**：
   - 提取文件内容和类型
   - 根据配置截断内容（默认 8000 字符）
   - 构建提示词并调用 OpenAI API
4. **AI 返回**：JSON 格式的目录结构
5. **后端转换**：
   - 解析 JSON 响应
   - 添加唯一 ID
   - 生成 Markdown 格式内容
6. **返回前端**：渲染目录和原文

## 隐私和安全考虑

1. **内容截断**：只发送文档前 8000 字符，避免发送过多敏感信息
2. **本地处理**：Markdown 文件在前端直接处理，不发送到服务器
3. **API 密钥**：存储在服务器端环境变量中，前端无法访问
4. **错误处理**：生产环境不返回详细错误信息

## 配置说明

所有配置项都在 `.env` 文件中：

```bash
# API 配置
OPENAI_API_KEY=你的密钥
OPENAI_API_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini

# 提示词配置
OPENAI_SYSTEM_PROMPT="系统提示词"
OPENAI_USER_PROMPT="用户提示词模板"

# 内容处理配置
OPENAI_MAX_CONTENT_LENGTH=8000
OPENAI_TRUNCATION_MESSAGE="...(内容过长已截断)"
```

## 示例

假设用户上传了一个 Python 教程文档，AI 会收到：

```json
{
  "role": "system",
  "content": "你是一个专业的文档分析助手..."
},
{
  "role": "user", 
  "content": "请分析以下txt文档内容，生成一个结构化的目录大纲。\n\n要求：...\n\n文档内容：\n# Python 基础教程\n\nPython 是一种解释型、面向对象...\n\n请以JSON格式返回..."
}
```

AI 返回：
```json
[
  {"title": "Python 简介", "level": 1},
  {"title": "安装和环境配置", "level": 1},
  {"title": "基础语法", "level": 1},
  {"title": "变量和数据类型", "level": 2},
  {"title": "控制流程", "level": 2},
  ...
]
```