# Markdown 阅读器

一个基于 Next.js 的简洁 Markdown 阅读器，支持文件上传、内容渲染和智能目录导航。

## 功能特性

- 📁 **文件上传**: 支持拖拽或点击上传 Markdown 文件
- 📖 **内容渲染**: 完整的 Markdown 语法支持，包括表格、代码块、列表等
- 🧭 **智能导航**: 自动生成浮动目录，支持快速跳转
- 🎨 **优雅界面**: 响应式设计，支持深色模式
- ⚡ **性能优化**: 基于 Next.js 14，快速加载和渲染

## 快速开始

### 安装依赖

\`\`\`bash
npm install
\`\`\`

### 启动开发服务器

\`\`\`bash
npm run dev
\`\`\`

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

\`\`\`bash
npm run build
npm start
\`\`\`

## 使用方法

1. **上传文件**: 将 Markdown 文件拖拽到上传区域，或点击选择文件
2. **阅读内容**: 文件上传后自动渲染和显示内容
3. **导航跳转**: 点击左上角目录按钮，选择章节快速跳转
4. **重新上传**: 点击"重新上传"按钮选择新文件

## 技术栈

- **Next.js 14**: React 框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架
- **react-markdown**: Markdown 解析和渲染
- **react-dropzone**: 文件拖拽上传
- **rehype-highlight**: 代码语法高亮
- **Font Awesome**: 图标库

## 项目结构

\`\`\`
src/
├── app/
│   ├── globals.css      # 全局样式
│   ├── layout.tsx       # 根布局
│   └── page.tsx         # 主页面
├── components/
│   ├── FileUpload.tsx   # 文件上传组件
│   ├── MarkdownRenderer.tsx # Markdown 渲染组件
│   └── FloatingTOC.tsx  # 浮动目录组件
└── lib/                 # 工具函数
\`\`\`

## 自定义配置

### 样式自定义

在 \`src/app/globals.css\` 中修改全局样式，包括：
- 主题颜色
- 字体设置
- Markdown 渲染样式
- 目录组件样式

### 功能扩展

- 添加更多文件格式支持
- 集成在线编辑功能
- 添加书签和历史记录
- 支持多文件管理

## 许可证

MIT License