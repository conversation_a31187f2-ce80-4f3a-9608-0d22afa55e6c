# Markdown 阅读器示例文档

这是一个示例 Markdown 文档，用于测试阅读器的各项功能。

## 功能特性

### 文件上传
- 支持拖拽上传 Markdown 文件
- 支持点击选择文件
- 自动识别 `.md` 格式文件

### 内容渲染
- **粗体文本**
- *斜体文本*
- `行内代码`
- [链接示例](https://example.com)

### 代码块支持

```javascript
function hello() {
    console.log("Hello, World!");
}
```

```python
def greet(name):
    return f"Hello, {name}!"
```

## 目录导航

### 自动生成
阅读器会自动分析文档标题结构，生成可点击的目录导航。

### 浮动显示
- 目录以浮动形式显示在左侧
- 点击目录项可快速跳转到对应章节
- 支持多级标题嵌套显示

#### 四级标题示例
这是四级标题的内容。

##### 五级标题示例
这是五级标题的内容。

###### 六级标题示例
这是六级标题的内容。

## 列表支持

### 无序列表
- 项目一
- 项目二
  - 子项目 A
  - 子项目 B
- 项目三

### 有序列表
1. 第一步
2. 第二步
   1. 子步骤 A
   2. 子步骤 B
3. 第三步

## 表格支持

| 功能 | 描述 | 状态 |
|------|------|------|
| 文件上传 | 拖拽或点击上传 | ✅ 完成 |
| Markdown 渲染 | 完整语法支持 | ✅ 完成 |
| 目录导航 | 自动生成，点击跳转 | ✅ 完成 |
| 代码高亮 | 多语言语法高亮 | ✅ 完成 |

## 引用块

> 这是一个引用块示例。
> 
> 可以包含多行内容，支持 **格式化** 文本。

## 分割线

---

## 使用说明

1. 点击上传区域或拖拽 Markdown 文件到页面
2. 文件上传后自动渲染内容
3. 点击左上角的目录按钮显示/隐藏导航
4. 点击目录项快速跳转到对应位置
5. 点击"重新上传"按钮返回上传界面

### 技术栈

- **前端框架**: Next.js 14
- **样式**: Tailwind CSS
- **Markdown 解析**: react-markdown
- **代码高亮**: rehype-highlight
- **文件上传**: react-dropzone
- **图标**: Font Awesome

## 结语

感谢使用 Markdown 阅读器！这个工具让您可以方便地阅读和导航 Markdown 文档。